"use client";
import { useClientContext } from "@/app/providers/client-provider";
import { GuidePlayerView as CoreGuidePlayerView } from "@repo/core/components/guide-player-view";
import { Guide } from "@repo/core/guide/guide";
import { Reference } from "@repo/core/types/data/comment";
import { GuideMode, GuideTheme } from "@repo/core/types/data/widget-guide";
import { FC, useCallback, useMemo } from "react";
import { useGuideViewContext } from "./guide-view-context";

interface GuidePlayerViewProps {
  className?: string;
  theme?: GuideTheme;
}

export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  className,
  theme,
}) => {
  const { studentUserInfo, screen } = useClientContext();

  const {
    showSubtitle,
    title,
    totalGuideCount,
    index,
    data,
    progress,
    trackEventWithLessonId,
    commentRef,
    ranges,
    referenceList,
    onClickReference,
    goto,
    setFollowMode,
    seekTo,
  } = useGuideViewContext();

  // 处理点击行的回调
  const handleLineClick = useCallback(
    (frame: number) => {
      seekTo(frame);
      setFollowMode();
      trackEventWithLessonId("doc_learn_from_here_click");
    },
    [seekTo, setFollowMode, trackEventWithLessonId]
  );

  // 处理滚动翻页的回调
  const handleScrollNext = useCallback(
    (index: number) => {
      goto(index);
    },
    [goto]
  );

  // 播控配置
  const playbackConfig = useMemo(
    () => ({
      enableGestures: true,
      enableDoubleTap: true,
      enableLongPress: true,
      showProgressToast: true,
      autoPlay: false,
      initialMode: GuideMode.follow,
      showSubtitle: showSubtitle.value,
    }),
    [showSubtitle.value]
  );

  // 事件回调
  const playbackCallbacks = useMemo(
    () => ({
      onLineClick: handleLineClick,
      onScrollNext: handleScrollNext,
      trackEvent: (eventId: string, params?: Record<string, unknown>) => {
        trackEventWithLessonId(eventId, !!params);
      },
      onProgressChange: (_frame: number) => {
        // 进度变化处理
      },
      onPlayStateChange: (_isPlaying: boolean) => {
        // 播放状态变化处理
      },
      onModeChange: (_mode: GuideMode) => {
        // 模式变化处理
      },
    }),
    [handleLineClick, handleScrollNext, trackEventWithLessonId]
  );

  // 进度配置
  const progressConfig = useMemo(
    () => ({
      enabled: true,
      initialProgress: progress,
      onProgressSave: (_progress: { frame: number }) => {
        // 保存进度到本地
      },
      onProgressLoad: () => progress,
    }),
    [progress]
  );

  // 输入属性
  const inputProps = useMemo(
    () => ({
      client: "stu",
      title,
      index,
      totalGuideCount,
      data,
      theme,
      commentRef,
      lineIdInRange: ranges[0]?.lineId,
    }),
    [title, index, totalGuideCount, data, theme, commentRef, ranges]
  );

  // 评论配置
  const commentConfig = useMemo(
    () => ({
      enabled: true,
      referenceList: referenceList as Reference[],
      onClickReference,
    }),
    [referenceList, onClickReference]
  );

  // 数字人配置
  const avatarConfig = useMemo(
    () => ({
      userId: studentUserInfo?.userId?.toString(),
      tag: "文稿组件",
    }),
    [studentUserInfo?.userId]
  );

  if (!data) {
    return <div>无数据</div>;
  }

  return (
    <CoreGuidePlayerView
      component={Guide}
      inputProps={inputProps}
      data={data}
      className={className}
      theme={theme}
      config={playbackConfig}
      callbacks={playbackCallbacks}
      progressConfig={progressConfig}
      screen={screen}
      avatarConfig={avatarConfig}
      commentConfig={commentConfig}
    />
  );
};
