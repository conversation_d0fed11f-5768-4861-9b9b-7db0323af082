import { batch, useSignal } from "@preact-signals/safe-react";
import { RefObject, useEffect } from "react";
import { useThrottledCallback } from "use-debounce";
import { GestureConfig } from "../types";

interface UseGuideGestureOptions extends GestureConfig {
  onPan?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
}

export const useGuideGesture = (
  ref: RefObject<HTMLDivElement | null>,
  options: UseGuideGestureOptions = {}
) => {
  const {
    enablePan = true,
    panThreshold = 72,
    onPan,
    onSwipeUp,
    onSwipeDown,
  } = options;

  const isMoving = useSignal(false);
  const startY = useSignal(0);
  const offsetY = useSignal(0);
  const hasTriggerOnPan = useSignal(false);

  const container = ref.current;

  const throttledTouchMove = useThrottledCallback((e: TouchEvent) => {
    if (!container || !enablePan) return;
    const touch = e.touches[0];
    if (!touch) return;
    const currentY = touch.clientY;
    const offset = currentY - startY.value;
    offsetY.value = offset;
    
    // 处理滑动手势
    if (
      hasTriggerOnPan.value === false &&
      isMoving.value === true &&
      Math.abs(offsetY.value) > panThreshold
    ) {
      if (offsetY.value > 0) {
        // 向下滑动
        onSwipeDown?.();
      } else {
        // 向上滑动
        onSwipeUp?.();
      }
      
      // 兼容原有的onPan逻辑
      onPan?.();
      hasTriggerOnPan.value = true;
    }
  }, 40);

  useEffect(() => {
    if (!container || !enablePan) {
      return;
    }
    
    const handleTouchStart = (e: TouchEvent) => {
      if (isMoving.value === true) return;
      const touch = e.touches[0];
      if (!touch) return;
      isMoving.value = true;
      startY.value = touch.clientY;
    };
    
    const handleTouchEnd = () => {
      batch(() => {
        isMoving.value = false;
        offsetY.value = 0;
        hasTriggerOnPan.value = false;
      });
    };

    container.addEventListener("touchstart", handleTouchStart);
    container.addEventListener("touchmove", throttledTouchMove);
    container.addEventListener("touchend", handleTouchEnd);
    
    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchend", handleTouchEnd);
      container.removeEventListener("touchmove", throttledTouchMove);
    };
  }, [
    container,
    enablePan,
    panThreshold,
    onPan,
    onSwipeUp,
    onSwipeDown,
    throttledTouchMove,
    isMoving,
    offsetY,
    startY,
    hasTriggerOnPan,
  ]);

  return {
    isMoving: isMoving.value,
    offsetY: offsetY.value,
  };
};
