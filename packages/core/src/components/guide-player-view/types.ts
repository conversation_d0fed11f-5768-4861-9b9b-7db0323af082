import { Signal } from "@preact-signals/safe-react";
import { PlayerRef } from "@remotion/player";
import { ComponentType, RefObject } from "react";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "../../types/data/widget-guide";
import { VolcenginePlayer } from "../volcengine-video/volcengine-video";

// 播控状态接口
export interface PlaybackState {
  isPlaying: boolean;
  playRate: number;
  progress: {
    frame: number;
  };
  canPlay: boolean;
  showPlayerControls: boolean;
  guideMode: GuideMode;
}

// 播控操作接口
export interface PlaybackActions {
  togglePlay: () => void;
  seekTo: (frame: number) => void;
  setFollowMode: () => void;
  setFreeMode: () => void;
  togglePlayerControls: () => void;
  hidePlayerControls: () => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  forwardTo: (seconds: number) => void;
}

// 事件回调接口
export interface PlaybackCallbacks {
  onLineClick?: (frame: number) => void;
  onScrollNext?: (index: number) => void;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onProgressChange?: (frame: number) => void;
  onModeChange?: (mode: GuideMode) => void;
  trackEvent?: (eventId: string, params?: Record<string, any>) => void;
}

// 播控配置接口
export interface PlaybackConfig {
  // 是否启用手势控制
  enableGestures?: boolean;
  // 是否启用双击播放/暂停
  enableDoubleTap?: boolean;
  // 是否启用长按快进
  enableLongPress?: boolean;
  // 是否显示进度提示
  showProgressToast?: boolean;
  // 是否自动播放
  autoPlay?: boolean;
  // 初始播放模式
  initialMode?: GuideMode;
  // 是否显示字幕
  showSubtitle?: boolean;
}

// 通用GuidePlayerView的Props接口
export interface GuidePlayerViewProps {
  // 必需参数
  component: ComponentType<any>;
  inputProps: Record<string, any>;
  data: GuideWidgetData;

  // 可选的样式和主题
  className?: string;
  theme?: GuideTheme;

  // 播控配置
  config?: PlaybackConfig;

  // 事件回调
  callbacks?: PlaybackCallbacks;

  // 屏幕尺寸（用于适配）
  screen?: {
    width: number;
    height: number;
  };

  // 数字人视频相关
  avatarConfig?: {
    userId?: string;
    tag?: string;
  };

  // 评论相关（可选）
  commentConfig?: {
    enabled?: boolean;
    referenceList?: any[];
    onClickReference?: (reference: any) => void;
  };

  // 进度管理（可选）
  progressConfig?: {
    enabled?: boolean;
    initialProgress?: { frame: number };
    onProgressSave?: (progress: { frame: number }) => void;
    onProgressLoad?: () => { frame: number };
  };
}

// 播控Context的类型
export interface GuidePlayerContextType {
  // 状态
  state: PlaybackState;

  // 操作方法
  actions: PlaybackActions;

  // 引用
  refs: {
    player: RefObject<PlayerRef | null>;
    volcenginePlayer: Signal<VolcenginePlayer | null>;
    container: RefObject<HTMLDivElement | null>;
  };

  // 数据
  data: GuideWidgetData;

  // 配置
  config: Required<PlaybackConfig>;

  // 计算属性
  computed: {
    durationInFrames: number;
    fps: number;
    videoDuration: number;
  };
}

// 手势处理配置
export interface GestureConfig {
  enablePan?: boolean;
  enableLongPress?: boolean;
  enableDoubleTap?: boolean;
  panThreshold?: number;
  longPressDuration?: number;
  doubleTapDelay?: number;
}

// 播控Context Provider的Props
export interface GuidePlayerProviderProps {
  data: GuideWidgetData;
  config?: PlaybackConfig;
  callbacks?: PlaybackCallbacks;
  progressConfig?: {
    enabled?: boolean;
    initialProgress?: { frame: number };
    onProgressSave?: (progress: { frame: number }) => void;
    onProgressLoad?: () => { frame: number };
  };
  children: React.ReactNode;
}
