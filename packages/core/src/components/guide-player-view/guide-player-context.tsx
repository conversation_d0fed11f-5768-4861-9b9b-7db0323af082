"use client";
import {
  createContext,
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import {
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
  batch,
} from "@preact-signals/safe-react";
import { PlayerRef, CallbackListener } from "@remotion/player";
import { VolcenginePlayer } from "../volcengine-video/volcengine-video";
import { GuideMode } from "../../types/data/widget-guide";
import {
  GuidePlayerContextType,
  GuidePlayerProviderProps,
  PlaybackState,
  PlaybackActions,
} from "./types";

const GuidePlayerContext = createContext<GuidePlayerContextType>(
  {} as GuidePlayerContextType
);

export const useGuidePlayerContext = () => {
  return useContext(GuidePlayerContext);
};

export const GuidePlayerProvider: FC<GuidePlayerProviderProps> = ({
  data,
  config = {},
  callbacks = {},
  progressConfig = {},
  children,
}) => {
  // 默认配置
  const defaultConfig = {
    enableGestures: true,
    enableDoubleTap: true,
    enableLongPress: true,
    showProgressToast: true,
    autoPlay: false,
    initialMode: GuideMode.follow,
    showSubtitle: true,
    ...config,
  };

  // 引用
  const refPlayer = useRef<PlayerRef | null>(null);
  const refVolcenginePlayer = useSignal<VolcenginePlayer | null>(null);
  const refContainer = useRef<HTMLDivElement | null>(null);

  // 基础状态
  const isPlaying = useSignal(false);
  const canPlay = useSignal(true);
  const playRate = useSignal(1);
  const showPlayerControls = useSignal(false);
  const guideMode = useSignal(defaultConfig.initialMode);
  const playRateBeforeLongPress = useSignal(1);

  // 进度状态
  const progress = useSignal(
    progressConfig.initialProgress || { frame: 0 }
  );

  // 计算属性
  const fps = data.avatar?.fps ?? 24;
  const durationInFrames = data.avatar?.durationInFrames + fps;
  const videoDuration = durationInFrames / fps;

  // 播控状态
  const state: PlaybackState = {
    isPlaying: isPlaying.value,
    playRate: playRate.value,
    progress: progress.value,
    canPlay: canPlay.value,
    showPlayerControls: showPlayerControls.value,
    guideMode: guideMode.value,
  };

  // 按帧跳转
  const seekTo = useCallback(
    (frame: number) => {
      if (!refPlayer.current) {
        return;
      }
      if (frame >= durationInFrames || frame < 0) {
        return;
      }
      const player = refPlayer.current;
      player.seekTo(frame);
      refVolcenginePlayer.value?.seek(frame / fps);
      isPlaying.value = true;
      
      callbacks.onProgressChange?.(frame);
      callbacks.trackEvent?.("doc_seek", { frame });
    },
    [durationInFrames, fps, callbacks]
  );

  // 播放/暂停切换
  const togglePlay = useCallback(() => {
    if (guideMode.value === GuideMode.free) return;
    isPlaying.value = !isPlaying.peek();
    callbacks.onPlayStateChange?.(isPlaying.value);
    callbacks.trackEvent?.(
      isPlaying.value ? "doc_play" : "doc_pause"
    );
  }, [guideMode, callbacks]);

  // 设置跟随模式
  const setFollowMode = useCallback(() => {
    if (guideMode.value === GuideMode.follow) return;
    guideMode.value = GuideMode.follow;
    isPlaying.value = true;
    callbacks.onModeChange?.(GuideMode.follow);
  }, [callbacks]);

  // 设置自由模式
  const setFreeMode = useCallback(() => {
    if (guideMode.value === GuideMode.free) return;
    guideMode.value = GuideMode.free;
    isPlaying.value = false;
    callbacks.onModeChange?.(GuideMode.free);
  }, [callbacks]);

  // 控制条显示/隐藏
  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, []);

  const hidePlayerControls = useCallback(() => {
    if (showPlayerControls.value) {
      showPlayerControls.value = false;
    }
  }, []);

  // 倍速播放
  const set3XPlayRate = useCallback(() => {
    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, []);

  const resetPlayRate = useCallback(() => {
    playRate.value = playRateBeforeLongPress.value;
  }, []);

  // 按秒跳转
  const forwardTo = useCallback(
    (seconds: number) => {
      const frame = Math.round(seconds * fps);
      seekTo(frame);
    },
    [seekTo, fps]
  );

  // 播控操作
  const actions: PlaybackActions = {
    togglePlay,
    seekTo,
    setFollowMode,
    setFreeMode,
    togglePlayerControls,
    hidePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    forwardTo,
  };

  // 播放器事件处理
  useEffect(() => {
    if (!refPlayer.current) {
      return;
    }
    const player = refPlayer.current;

    const handleTimeUpdate: CallbackListener<"timeupdate"> = (e) => {
      progress.value = { frame: e.detail.frame };
      progressConfig.onProgressSave?.({ frame: e.detail.frame });
    };

    const handleEnded: CallbackListener<"ended"> = () => {
      isPlaying.value = false;
      callbacks.onPlayStateChange?.(false);
      callbacks.trackEvent?.("doc_ended");
    };

    player.addEventListener("timeupdate", handleTimeUpdate);
    player.addEventListener("ended", handleEnded);

    return () => {
      player.removeEventListener("timeupdate", handleTimeUpdate);
      player.removeEventListener("ended", handleEnded);
    };
  }, [callbacks, progressConfig]);

  // 播放状态同步
  useSignalEffect(() => {
    if (refVolcenginePlayer.value === null) return;
    if (isPlaying.value && canPlay.value) {
      refPlayer.current?.play();
      refVolcenginePlayer.value?.play();
    } else {
      refPlayer.current?.pause();
      refVolcenginePlayer.value?.pause();
    }
  });

  // Context值
  const value: GuidePlayerContextType = {
    state,
    actions,
    refs: {
      player: refPlayer,
      volcenginePlayer: refVolcenginePlayer,
      container: refContainer,
    },
    data,
    config: defaultConfig,
    computed: {
      durationInFrames,
      fps,
      videoDuration,
    },
  };

  return (
    <GuidePlayerContext.Provider value={value}>
      {children}
    </GuidePlayerContext.Provider>
  );
};
