"use client";
import loadingData from "@/public/lottie/page-loading.json";
import { cn } from "@/utils";
import Lottie from "lottie-react";

export default function PageLoading({
  text = "网络不通畅，页面努力加载中...",
  className,
}: {
  text?: string;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex size-full flex-col items-center justify-center gap-1",
        className
      )}
    >
      <Lottie className="size-17.5" animationData={loadingData} loop={true} />

      <div className="text-gray-4 text-sm/normal">{text}</div>
    </div>
  );
}
