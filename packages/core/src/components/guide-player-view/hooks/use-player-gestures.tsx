import { useSignal } from "@preact-signals/safe-react";
import { useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { GestureConfig } from "../types";

interface UsePlayerGesturesOptions extends GestureConfig {
  onSingleTap?: () => void;
  onDoubleTap?: () => void;
  onLongPressStart?: () => void;
  onLongPressEnd?: () => void;
  // 排除评论区域的选择器
  excludeSelectors?: string[];
}

export const usePlayerGestures = (options: UsePlayerGesturesOptions = {}) => {
  const {
    enableDoubleTap = true,
    enableLongPress = true,
    doubleTapDelay = 300,
    longPressDuration = 500,
    onSingleTap,
    onDoubleTap,
    onLongPressStart,
    onLongPressEnd,
    excludeSelectors = ["[data-name=line-container]"],
  } = options;

  const isComment = useSignal(false);

  // 检查是否在排除区域内
  const isInExcludedArea = useCallback((target: EventTarget | null) => {
    if (!target || !(target instanceof HTMLElement)) return false;
    
    for (const selector of excludeSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        if (element.contains(target)) {
          return true;
        }
      }
    }
    return false;
  }, [excludeSelectors]);

  // 长按处理
  const longPressHandlers = useLongPress(
    (e) => {
      if (!enableLongPress) return;
      
      if (isInExcludedArea(e.target)) {
        isComment.value = true;
        return;
      }
      
      onLongPressStart?.();
    },
    {
      onFinish: () => {
        if (!enableLongPress) return;
        
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        
        onLongPressEnd?.();
      },
      detect: LongPressEventType.Touch,
      threshold: longPressDuration,
    }
  );

  // 单击处理
  const handleClick = useCallback(() => {
    onSingleTap?.();
  }, [onSingleTap]);

  // 双击处理
  const handleDoubleClick = useCallback(() => {
    if (!enableDoubleTap) return;
    onDoubleTap?.();
  }, [enableDoubleTap, onDoubleTap]);

  // 双击手势处理
  const doubleTapHandlers = useDoubleTap(handleDoubleClick, doubleTapDelay, {
    onSingleTap: handleClick,
  });

  return {
    // 手势处理器
    gestureHandlers: enableDoubleTap || enableLongPress ? {
      ...(enableDoubleTap ? doubleTapHandlers : { onClick: handleClick }),
      ...(enableLongPress ? longPressHandlers() : {}),
    } : {},
    
    // 状态
    isComment: isComment.value,
  };
};
