"use client";

import { useUmeng } from "@/hooks/useUmeng";
import { PanelItem } from "@/services/homework";
import { Card, CardContent, CardFooter } from "@/ui/card";
import { ScrollArea } from "@/ui/scroll-area";
import { Skeleton } from "@/ui/skeleton";
import GiLLoading from "@/ui/spin";
import { UmengCategory } from "@/utils";
import { batch, useSignal } from "@preact-signals/safe-react";
import { useMount, useUpdateEffect } from "ahooks";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  activeTabSignal,
  useTaskContext,
} from "../../../_context/task-context";
import { QuestionDetailDrawer } from "./components/QuestionDetailDrawer";
import QuestionListWrapper from "./components/QuestionListWrapper";
import { ResultFloatingButton } from "./components/ResultFloatingButton";
import Toolbar from "./components/Toolbar";
import {
  selectQuestion,
  useAnswerResults,
  useAnswerResultsPolling,
} from "./store/answers";

export default function Results() {
  const { classData, taskId, viewMode, firstLoading } = useTaskContext();
  useUmeng(
    UmengCategory.HOMEWORK,
    viewMode.value === "student"
      ? "homework_list_report_student_detail_question_tab"
      : "homework_list_report_question_tab"
  );

  const { panel } = useAnswerResults();

  const {
    loading,
    // data,
    fetchAnswerResults,
    fetchParams,
    selectedQuestionId,
    answerResultsState,
    pagination,
  } = useAnswerResults();
  const isPanlLoading = useSignal(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const {
    data: pollingData,
    loading: pollingLoading,
    run,
    cancel,
  } = useAnswerResultsPolling();
  const [isInit, setIsInit] = useState(false);
  // 使用 useRef 跟踪数据加载状态
  const dataLoaded = useRef(false);
  const runDelayRef = useRef<NodeJS.Timeout>(null);

  // 监听标签页变化和数据加载
  useEffect(() => {
    // 如果当前标签页是答题结果，并且有必要的参数，则加载数据
    // fetchAnswerResults(fetchParams.value);
    if (
      activeTabSignal.value === "results" &&
      classData.value.taskId &&
      classData.value?.assignId
    ) {
      // 如果数据尚未加载，则加载数据
      // if (!dataLoaded.current) {
      // 加载答题数据
      // fetchAnswerResults(fetchParams.value);
      // console.log("run fetchAnswerResults");
      run();
      // 标记数据已加载
      dataLoaded.current = true;
      // }
    } else {
      // 如果不是答题结果标签页，重置加载标志，以便下次切换到此标签页时重新加载
      dataLoaded.current = false;
      cancel();
      if (runDelayRef.current) {
        clearTimeout(runDelayRef.current);
      }
    }

    // 组件卸载时重置加载标志
    return () => {
      if (activeTabSignal.value !== "results") {
        dataLoaded.current = false;
        cancel();
        if (runDelayRef.current) {
          clearTimeout(runDelayRef.current);
        }
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    taskId,
    classData.value,
    viewMode.value,
    // studentDataSignal.value.studentId,
    activeTabSignal.value,
    fetchAnswerResults,
    fetchParams,
  ]);

  useMount(() => {
    fetchAnswerResults(fetchParams.value);
  });

  useUpdateEffect(() => {
    run();
  }, [fetchParams.value]);

  useUpdateEffect(() => {
    if (!isInit) {
      answerResultsState.value.loading = pollingLoading;
      setIsInit(true);
      firstLoading.value = false;
    } else {
      answerResultsState.value.loading = false;
    }
  }, [pollingLoading]);

  useUpdateEffect(() => {
    let response;
    let panelResponse: { panel: PanelItem[] } = {
      panel: [],
    };
    if (pollingData && pollingData[0]) {
      response = pollingData[0];
      panelResponse = pollingData[1];
    } else {
      response = pollingData;
    }
    batch(() => {
      answerResultsState.value = {
        data: response,
        panel: panelResponse?.panel,
        loading: false,
        selectedQuestionId: null,
      };
      // 如果页码超过总页数，重置到第一页
      if (response) {
        if (
          fetchParams.value.page >
          Math.ceil(response.pageInfo.total / fetchParams.value.pageSize)
        ) {
          pagination.value.current = 1;
        }
      }
    });
  }, [pollingData]);

  // 监听选中的题目ID变化，打开抽屉
  // useEffect(() => {
  //   console.log("question detail selected", performance.now());
  //   if (selectedQuestionId.value) {
  //     if (drawerOpen !== true) setDrawerOpen(true);
  //     cancel();
  //     if (runDelayRef.current) {
  //       clearTimeout(runDelayRef.current);
  //     }
  //   }
  // }, [selectedQuestionId.value]);

  const openQuestionDrawer = useCallback(() => {
    setDrawerOpen(true);
    cancel();
  }, []);

  // 如果数据正在加载，只将问题列表区域替换为骨架屏
  // console.log("results panel", panel);
  // console.log("question detail drawer", performance.now(), drawerOpen);
  return (
    <div className="relative flex h-full flex-col">
      {loading.value ? (
        <GiLLoading
          loading={true}
          mask={true}
          className="flex h-[calc(100vh-200px)] items-center justify-center"
        />
      ) : (
        <>
          <ResultFloatingButton
            resultPanlData={panel}
            isLoading={isPanlLoading}
            openQuestionDrawer={openQuestionDrawer}
          />

          {/* 将整个内容区域放在一个滚动容器中 */}
          <ScrollArea className="flex-1 overflow-auto">
            <div className="flex flex-col">
              {/* 作业进度元素 */}
              {/* <div className="bg-primary-5 flex items-center gap-2 rounded-[0.25rem] px-10 py-1">
            <div className="bg-primary-2 flex h-5 w-5 items-center justify-center rounded-full text-white">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="4"
                height="11"
                viewBox="0 0 4 11"
                fill="none"
              >
                <path
                  d="M2 5.5L2 9.66667"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M2 2.16675L2 2.58342"
                  stroke="white"
                  strokeWidth="2.6"
                  strokeLinecap="round"
                />
              </svg>
            </div>
            <div className="text-gray-2 text-sm leading-[150%]">
              目前已完成作业进度{" "}
              {loading.value ? (
                <Skeleton className="inline-block h-4 w-8" />
              ) : (
                `${data.value?.progress || 0}%`
              )}
            </div>
          </div> */}

              {/* 工具栏 - 普通堆叠 */}
              <Toolbar />

              {/* 题目列表 */}
              {loading.value ? (
                <div className="px-4">
                  <div className="flex flex-col gap-y-4 pb-2">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <QuestionItemSkeleton key={i} />
                    ))}
                  </div>
                </div>
              ) : (
                <QuestionListWrapper
                  onClick={() => {
                    setDrawerOpen(true);
                    cancel();
                    if (runDelayRef.current) {
                      clearTimeout(runDelayRef.current);
                    }
                  }}
                />
              )}
            </div>
          </ScrollArea>

          {/* 题目详情抽屉 */}
          <QuestionDetailDrawer
            open={drawerOpen}
            onOpenChange={(open) => {
              setDrawerOpen(open);
              if (open === true) {
                cancel();
              } else {
                runDelayRef.current = setTimeout(() => {
                  run();
                }, 60000);
              }
              if (!open) {
                selectQuestion(null);
              }
            }}
            questionId={selectedQuestionId.value || undefined}
            floatButton={
              <ResultFloatingButton
                resultPanlData={panel}
                isLoading={isPanlLoading}
              />
            }
          />
        </>
      )}
    </div>
  );
}

export function QuestionItemSkeleton() {
  return (
    <Card className="border-none px-5 pb-3 pt-3 shadow-none">
      <CardContent className="p-0">
        <div className="flex gap-2">
          {/* 题目序号 */}
          <div className="bg-fill-gray-2 mt-1 flex h-8 min-w-8 items-center justify-center rounded-2xl p-1">
            <Skeleton className="h-5 w-5" />
          </div>

          <div className="w-full flex-1 overflow-hidden">
            {/* 题目内容 */}
            <div className="mb-3 flex flex-col gap-y-2">
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-5 w-4/5" />
              <Skeleton className="h-5 w-2/3" />
            </div>

            {/* 选项区域 */}
            <div className="mt-4 flex flex-col gap-y-2">
              <div className="flex items-start gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-5 w-4/5" />
              </div>
              <div className="flex items-start gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-5 w-3/4" />
              </div>
              <div className="flex items-start gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-5 w-4/5" />
              </div>
              <div className="flex items-start gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-5 w-3/5" />
              </div>
            </div>

            {/* 标签区域 */}
            <div className="mt-3 flex items-center gap-2">
              <Skeleton className="h-5 w-16 rounded-full" />
              <Skeleton className="h-5 w-16 rounded-full" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
          </div>
        </div>
      </CardContent>

      {/* 底部区域 */}
      <CardFooter className="border-primary-5 mt-3 flex w-full flex-col items-center border-t border-dashed !pt-3">
        <div className="flex h-[3.5rem] w-full items-center justify-between pl-6">
          {/* 左侧内容 */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-16" />
          </div>

          {/* 右侧内容 */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20 rounded-full" />
            <Skeleton className="h-8 w-16 rounded-full" />
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
