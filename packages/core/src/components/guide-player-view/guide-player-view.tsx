"use client";
import { FC, useCallback, useEffect, useMemo } from "react";
import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { GuideMode } from "../../types/data/widget-guide";
import { GuidePlayerViewProps } from "./types";
import { GuidePlayerProvider, useGuidePlayerContext } from "./guide-player-context";
import { usePlayerGestures } from "./hooks/use-player-gestures";
import { useGuideGesture } from "./hooks/use-guide-gesture";

const VolcengineVideo = dynamic(
  () => import("../volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

// 内部播放器组件
const GuidePlayerCore: FC<Omit<GuidePlayerViewProps, 'data' | 'config' | 'callbacks' | 'progressConfig'>> = ({
  component,
  inputProps,
  className,
  theme,
  screen = { width: 1000, height: 600 },
  avatarConfig = {},
  commentConfig = {},
}) => {
  const {
    state,
    actions,
    refs,
    data,
    config,
    computed,
  } = useGuidePlayerContext();

  const showLastProgressToast = useSignal(false);

  // 手势处理
  const { gestureHandlers } = usePlayerGestures({
    enableDoubleTap: config.enableDoubleTap,
    enableLongPress: config.enableLongPress,
    onSingleTap: actions.togglePlayerControls,
    onDoubleTap: () => {
      actions.togglePlay();
      // 可以通过callbacks传入trackEvent
    },
    onLongPressStart: actions.set3XPlayRate,
    onLongPressEnd: () => {
      actions.resetPlayRate();
      // 可以通过callbacks传入trackEvent
    },
  });

  // 滑动手势处理
  useGuideGesture(refs.container, {
    enablePan: config.enableGestures,
    onPan: actions.setFreeMode,
  });

  // 处理点击行的回调
  const handleLineClick = useCallback((frame: number) => {
    actions.seekTo(frame);
    actions.setFollowMode();
  }, [actions]);

  // 处理滚动翻页的回调
  const handleScrollNext = useCallback((index: number) => {
    // 这里需要通过callbacks传入goto方法
  }, []);

  // 进度提示
  useEffect(() => {
    if (!config.showProgressToast) return;
    if (state.guideMode === GuideMode.free) return;
    if (showLastProgressToast.value === true) return;
    if (state.progress.frame > 0) {
      // 这里可以通过callbacks传入toast显示方法
      console.log("已从上次进度开始学习");
    }
    showLastProgressToast.value = true;
  }, [state.progress, showLastProgressToast, state.guideMode, config.showProgressToast]);

  // 合并inputProps
  const mergedInputProps = useMemo(() => ({
    ...inputProps,
    data,
    theme,
    showSubtitle: config.showSubtitle,
    onLineClick: handleLineClick,
    refContainer: refs.container,
    ...(commentConfig.enabled && {
      referenceList: commentConfig.referenceList,
      onClickReference: commentConfig.onClickReference,
    }),
    onScrollFlip: handleScrollNext,
  }), [
    inputProps,
    data,
    theme,
    config.showSubtitle,
    handleLineClick,
    refs.container,
    commentConfig,
    handleScrollNext,
  ]);

  if (!data) {
    return <div>无数据</div>;
  }

  const { avatar } = data;

  return (
    <div
      {...gestureHandlers}
      data-name="guide-player"
      className="relative h-screen w-full"
      ref={refs.container}
    >
      <Player
        ref={refs.player}
        className={cn("h-full w-full", className)}
        style={{ height: screen.height }}
        component={component}
        inputProps={mergedInputProps}
        initialFrame={state.progress.frame}
        durationInFrames={computed.durationInFrames}
        fps={avatar.fps}
        playbackRate={state.playRate}
        allowFullscreen={false}
        compositionWidth={screen.width}
        compositionHeight={screen.height}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
      
      {/* 数字人视频 */}
      {avatar.url && (
        <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
          <VolcengineVideo
            ref={refs.volcenginePlayer}
            className="relative flex h-full w-full flex-col items-center justify-end"
            src={avatar.url}
            startTime={state.progress.frame / avatar.fps}
            playRate={state.playRate}
            userId={avatarConfig.userId}
            tag={avatarConfig.tag || "文稿组件"}
          />
        </div>
      )}
    </div>
  );
};

// 主组件
export const GuidePlayerView: FC<GuidePlayerViewProps> = ({
  data,
  config,
  callbacks,
  progressConfig,
  ...props
}) => {
  return (
    <GuidePlayerProvider
      data={data}
      config={config}
      callbacks={callbacks}
      progressConfig={progressConfig}
    >
      <GuidePlayerCore {...props} />
    </GuidePlayerProvider>
  );
};
